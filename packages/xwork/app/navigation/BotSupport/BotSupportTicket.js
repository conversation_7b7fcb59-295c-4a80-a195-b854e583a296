import { Wrapper<PERSON>ontainer } from '@components';
import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import * as GestureHandler from 'react-native-gesture-handler';
import { translate } from '@mwg-kits/languages';
import Toast from 'react-native-toast-message';
import { useSelector } from 'react-redux';
import VideoPlayer from 'react-native-video-controls';

const MyStack = createStackNavigator();

const BotSupportTicketPlugin = (props) => {
    const navigation = useNavigation();
    const screenName = useSelector(state => state?.dashboardReducer?.screenName);
    const profile = useSelector(
        state => state?.profileReducer?.fullProfile.data,
    );

    const { codeSupport } = props?.route?.params || {};

    // // Thêm state để force reset khi typeCode thay đổi
    // const [key, setKey] = useState(0);
    // const [currentTypeCode, setCurrentTypeCode] = useState(null);

    // useEffect(() => {
    //     if (codeSupport?.code && codeSupport.code !== currentTypeCode) {
    //         // Force re-mount component khi typeCode thay đổi
    //         setKey(prev => prev + 1);
    //         setCurrentTypeCode(codeSupport.code);
    //     }
    // }, [codeSupport?.code, currentTypeCode]);

    // // Không render gì nếu chưa có typeCode
    // if (!codeSupport?.code) {
    //     return null;
    // }

    return (
        <WrapperContainer
            key={key} // Force re-mount khi key thay đổi
            infoModule={{
                namePlugin: 'BotSupportTicket',
                container: './Main',
            }}
            navigation={navigation}
            MyStack={MyStack}
            GestureHandler={GestureHandler}
            profile={profile}
            Toast={Toast}
            name={codeSupport.name}
            typeCode={codeSupport.code}
            VideoPlayer={VideoPlayer}
        />
    );
};

export default BotSupportTicketPlugin;

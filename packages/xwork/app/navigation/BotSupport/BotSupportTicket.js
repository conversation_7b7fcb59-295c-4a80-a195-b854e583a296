import { WrapperContainer } from '@components';
import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import * as GestureHandler from 'react-native-gesture-handler';
import { translate } from '@mwg-kits/languages';
import Toast from 'react-native-toast-message';
import { useSelector } from 'react-redux';
import VideoPlayer from 'react-native-video-controls';

const MyStack = createStackNavigator();

const BotSupportTicketPlugin = (props) => {
    const navigation = useNavigation();
    const screenName = useSelector(state => state?.dashboardReducer?.screenName);
    const profile = useSelector(
        state => state?.profileReducer?.fullProfile.data,
    );

    const { codeSupport: initialCodeSupport } = props?.route?.params || {};

    // State để quản lý codeSupport, reset về rỗng mỗi lần component mount
    const [codeSupport, setCodeSupport] = useState({});

    // Reset codeSupport về rỗng khi component mount, sau đó set giá trị từ params
    useEffect(() => {
        // Reset về rỗng trước
        setCodeSupport({});

        // Sau đó set giá trị từ params nếu có
        if (initialCodeSupport) {
            setCodeSupport(initialCodeSupport);
        }
    }, [initialCodeSupport]);

    return (
        <WrapperContainer
            infoModule={{
                namePlugin: 'BotSupportTicket',
                container: './Main',
            }}
            navigation={navigation}
            MyStack={MyStack}
            GestureHandler={GestureHandler}
            profile={profile}
            Toast={Toast}
            name={codeSupport.name}
            typeCode={codeSupport.code}
            VideoPlayer={VideoPlayer}
        />
    );
};

export default BotSupportTicketPlugin;

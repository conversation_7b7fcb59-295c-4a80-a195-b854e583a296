import {
  helper,
  storage,
  answerIncomingVideoCall,
  declineIncomingVideoCall,
  getAccessApp,
} from '@common';
import { RepackMWG } from '@mwg-kits/core';
import { ENUM, ENUM_STORAGE, ModulesApp, portsModule } from '@constants';
import { NavigationContainer } from '@react-navigation/native';
import { useEffect, useRef } from 'react';
import Config from 'react-native-config';
import { enableScreens } from 'react-native-screens';
import { useDispatch, useSelector } from 'react-redux';
import { GlobalStore } from 'redux-micro-frontend';
import AuthenNavigator from './authenNavigator';
import MainNavigator from './mainNavigator';
import OnBoardingNavigator from './onBoardingNavigator';
import SplashStackNavigator from './splashNavigator';
import { globalXworkStore } from '@globalStore';

import R<PERSON>all<PERSON>eep from 'react-native-callkeep';
import { Platform, NativeModules } from 'react-native';


const { GetPayloadDataModule } = NativeModules;

enableScreens();

var globalStore;

const AppContainer = props => {
  const stackApp = useSelector(state => state.authenReducer.stackApp);
  const { incomingCallData } = useSelector(state => state.videoCallReducer);
  const dispatch = useDispatch();
  const routeNameRef = useRef(null);
  const navigationRef = useRef(null);
  const linking = {
    prefixes: ['xwork://', 'xmanager://'],
    config: {
      screens: {
        SSO: {
          path: 'xmanager?action=update&bundleID=mwg.tgdd.xwork',
        },
      },
    },
  };
  const onChangeScreen = async () => {
    const previousRouteName = routeNameRef.current;
    const currentRouteName = navigationRef.current?.getCurrentRoute()?.name;
    if (currentRouteName && previousRouteName !== currentRouteName) {
      // global.props.logScreenView(currentRouteName);
    }

    // Save the current route name for later comparison
    routeNameRef.current = currentRouteName;
  };
  const onReadyScreen = () => {
    routeNameRef.current = navigationRef.current?.getCurrentRoute()?.name;
  };

  const initRepack = async () => {
    try {
      // const access_token = await storage.getItem(ENUM_STORAGE.TOKEN_ACCESS);
      const { access_token } = getAccessApp();
      if (!access_token) return;
      const repack = new RepackMWG({
        urlRemote: `${Config.HOST_MICRO_APP}${Config.MICRO_APP_SERVICE}/api/micro/main/version/latest`,
        infoApp: {
          appName: 'XWORK',
          platform: 'IOS',
          versionApp: helper.getVersion(),
          environment: helper.getENVUppercase(),
          idToken: access_token,
          deviceToken: '',
        },
        ports: portsModule,
        modules: ModulesApp,
        isDev: true,
        cache: !__DEV__,
      });
      // repack.setStorage();
      repack.initScriptManager();
      initGlobalStore();
    } catch (error) {
      console.log('initRepack_error', error);
    }
  };

  const initGlobalStore = () => {
    globalStore = GlobalStore.Get();
    globalStore.RegisterStore('XworkStore', globalXworkStore, [
      GlobalStore.AllowAll,
    ]);
  };

  useEffect(() => {
    initRepack();
    handleCall();
    if (!__DEV__) {
      initAnalytics();
    }
  }, [stackApp]);

  const initAnalytics = async () => {
    const userInfo = await storage.getItem(ENUM_STORAGE.USER_INFO);
    const info = JSON.parse(userInfo);
    const userName = info?.userName ? info.userName : 'GUEST';
    amplitude.init(userName, {
      app_name: 'XWORK',
      logLevel: 3,
      tracking_session_events: true,
      session_id: new Date().getTime(),
    });
  };

  const handleCall = () => {
    if (Platform.OS === 'ios') {
      RNCallKeep.addEventListener('answerCall', ({ callUUID }) => {
        GetPayloadDataModule.getStoredPayloadData()
          .then(async payloadData => {
            if (
              helper.IsValidateObject(payloadData) &&
              !helper.IsEmptyString(payloadData?.callerId)
            ) {
              answerIncomingVideoCall(payloadData);
            }
          })
          .catch(error => {
            // Handle error
            RNCallKeep.endCall(callUUID);
            global.props.alert({
              show: true,
              message: `Có lỗi xảy ra, vui lòng gọi lại ${error}`,
              type: 'info',
              onConfirmPressed: () => {
                global.props.alert({ show: false });
              },
            });
          });
      });
      RNCallKeep.addEventListener('endCall', ({ callUUID }) => {
        GetPayloadDataModule.getStoredPayloadData()
          .then(payloadData => {
            if (
              helper.IsValidateObject(payloadData) &&
              helper.hasProperty(payloadData, 'USERNAME_DES') &&
              !helper.IsValidateObject(incomingCallData?.uuid)
            ) {
              declineIncomingVideoCall(payloadData);
            }
          })
          .catch(error => {
            // Handle error
          });
      });
    }
  };

  const getStack = () => {
    switch (stackApp) {
      case ENUM.ENUM_STACK.FISRT_LOGIN_SHOW_ON_BOARDING:
        return (
          <NavigationContainer
            ref={navigationRef}
            linking={linking}
            onReady={onReadyScreen}
            onStateChange={onChangeScreen}>
            <OnBoardingNavigator />
          </NavigationContainer>
        );

      case ENUM.ENUM_STACK.LOGIN_AUTHEN:
        return (
          <NavigationContainer
            ref={navigationRef}
            linking={linking}
            onReady={onReadyScreen}
            onStateChange={onChangeScreen}>
            <AuthenNavigator />
          </NavigationContainer>
        );

      case ENUM.ENUM_STACK.HOME_STACK:
        return (
          <NavigationContainer
            ref={navigationRef}
            linking={linking}
            onReady={onReadyScreen}
            onStateChange={onChangeScreen}>
            <MainNavigator />
          </NavigationContainer>
        );

      default:
        return (
          <NavigationContainer
            ref={navigationRef}
            linking={linking}
            onReady={onReadyScreen}
            onStateChange={onChangeScreen}>
            <SplashStackNavigator />
          </NavigationContainer>
        );
    }
  };

  return getStack();
};

export default AppContainer;

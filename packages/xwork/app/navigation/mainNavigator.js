import { createStackNavigator } from '@react-navigation/stack';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect } from 'react';
import { enableScreens } from 'react-native-screens';
import EditDashboard from '../container/Dashboard/EditDashboard';
import EcontractPlugin from './XHrmNavigation/EcontractPlugin';
import PromotionPluginScreen from './XHrmNavigation/PromotionPlugin';
import PromotionReviewPluginScreen from './XHrmNavigation/PromotionReviewPlugin';
import GiftPluginScreen from './giftPlugin';
import HomeTab from './homeNavigator';
import XticketPluginScreen from './xticketPlugin';
import KYCProfilePlugin from './XHrmNavigation/KYCProfilePlugin';
import IntergrationPluginScreen from './intergrationPlugin';
import FaceMWGScreen from './XHrmNavigation/FaceMWGPlugin';
import CommitmentPluginScreen from './commitment';
import ElectronicPluginScreen from './IotNavigation/ElectronicPlugin';
import SecurityWarningPluginScreen from './IotNavigation/SecurityWarningPlugin';
import SearchUtilities from '../container/Home/SearchUtilities';
import MapsContributePluginScreen from './mapsContribute';
import CulturePluginScreen from './Culture';
import MaintainPluginScreen from './Maintain';
import DeviceControlScreen from './IotNavigation/DeviceControlPlugin';
import AppreciatePluginScreen from './XHrmNavigation/AppreciatePlugin';
import BannerView from '../container/Dashboard/WebViewScreen/index';
import Notification from './notificationPlugin';
import ReportTicketScreen from './reportTicketNavigator';
import SupportTicketManagementScreen from './TicketNavigation/SupportTicketManagement';
import FeedbackPluginScreen from './feedbackPlugin';
import ITOPManagementScreen from './ITOPManagementPlugin';
import GetGeolocationScreen from './getGeolocation';
import ESGNewsScreen from './esgNews';
import CreateQuickTicket from '../../app/container/Home/CreateQuickTicket';
import CallCenterScreen from './callCenterNavigator';
import AuthorizeScreen from './authorizePlugin';
import CustomerServiceScreen from './customerServiceScreen';
import PFControllerScreen from './PFControllerNavigator';
import TimeKeepingPlugin from './XHrmNavigation/TimeKeeping';
import ChangeHealthLocationPlugin from './XHrmNavigation/ChangeHealthLocationPlugin';
import WorkplaceAssessmentPlugin from './XHrmNavigation/WorkplaceAssessmentPlugin';
import CardLinkMembershipPlugin from './BhxNavigation/CardLinkMembershipPlugin';
import DiscountedPurchaseApprovalPlugin from './XHrmNavigation/DiscountedPurchaseApproval';
import ApprovePhoneNumberPlugin from './XHrmNavigation/ApprovePhoneNumber';
import EvaluationSurveyPlugin from './XHrmNavigation/EvaluationSurveyPlugin';
import OsinEvaluationPlugin from './XHrmNavigation/OsinEvaluationPlugin';
import PaymentApproval from './XHrmNavigation/PaymentApproval';
import TrackOrderPlugin from './BhxNavigation/TrackingOrderPlugin';
import GenerateBarcodePlugin from './BhxNavigation/GenerateBarcodePlugin';
import DeclarationElectricAndWaterPlugin from './BhxNavigation/DeclarationElectricAndWaterPlugin';
import OutputVoucherLogRequestPlugin from './BhxNavigation/OutputVoucherLogRequestPlugin';
import QCProfileManagementPlugin from './BhxNavigation/QCProfileManagementPlugin';
import DeviceProfilePlugin from './BhxNavigation/DeviceProfilePlugin';
import DeliveriesPlugin from './LogisticsNavigation/DeliveriesPlugin';
import AssetMWGPlugin from './AssetMWG';

import { Feedback } from '@common';
import { useSelector } from 'react-redux';
import MailPlugin from './OfficeNavigation/MailPlugin';
import BotSupportTicketPlugin from './BotSupport/BotSupportTicket';

enableScreens();
const MainStack = createStackNavigator();

const MainAppNavigator = () => {
  const navigation = useNavigation();
  const { isReceivedIncomingVideoCall } = useSelector(
    state => state.videoCallReducer,
  );

  useEffect(() => {
    if (isReceivedIncomingVideoCall) {
      navigation.navigate('VideoCallScreen');
    }
  }, [isReceivedIncomingVideoCall]);

  return (
    <>
      <MainStack.Navigator
        // initialRouteName="AssetMWG"
        screenOptions={{
          headerShown: false,
        }}>
        <MainStack.Screen name="Home" component={HomeTab} />
        <MainStack.Screen name="Gifts" component={GiftPluginScreen} />
        <MainStack.Screen
          name="Xticket"
          component={XticketPluginScreen}
          options={{
            gestureEnabled: false,
          }}
        />
        <MainStack.Screen
          name="IntergrationPlugin"
          component={IntergrationPluginScreen}
        />
        <MainStack.Screen
          name="CreateQuickTicket"
          component={CreateQuickTicket}
        />

        <MainStack.Screen name="SearchUtilities" component={SearchUtilities} />

        <MainStack.Screen name="EditDashboard" component={EditDashboard} />
        <MainStack.Screen name="Promotion" component={PromotionPluginScreen} />
        <MainStack.Screen
          name="PromotionReview"
          component={PromotionReviewPluginScreen}
        />

        <MainStack.Screen
          options={{
            gestureEnabled: false,
          }}
          name="EContract"
          component={EcontractPlugin}
        />
        <MainStack.Screen name="KYCProfile" component={KYCProfilePlugin} />
        <MainStack.Screen name="FaceMWG" component={FaceMWGScreen} />
        <MainStack.Screen
          name="Commitment"
          component={CommitmentPluginScreen}
        />
        <MainStack.Screen
          name="ElectronicPrice"
          component={ElectronicPluginScreen}
        />
        <MainStack.Screen
          name="MapsContribute"
          component={MapsContributePluginScreen}
          options={{ gestureEnabled: false }}
        />
        <MainStack.Screen name="Culture" component={CulturePluginScreen} />
        <MainStack.Screen name="Maintain" component={MaintainPluginScreen} />
        <MainStack.Screen
          name="DeviceControl"
          component={DeviceControlScreen}
        />
        <MainStack.Screen name="Geolocation" component={GetGeolocationScreen} />
        <MainStack.Screen
          name="AppreciatePlugin"
          component={AppreciatePluginScreen}
        />
        <MainStack.Screen
          name="SecurityWarning"
          component={SecurityWarningPluginScreen}
          options={{ gestureEnabled: false }}
        />
        <MainStack.Screen name="BannerScreen" component={BannerView} />
        <MainStack.Screen name="ESGNews" component={ESGNewsScreen} />
        <MainStack.Screen name="NotificationPlugin" component={Notification} />
        <MainStack.Screen name="ReportTicket" component={ReportTicketScreen} />
        <MainStack.Screen
          name="SupportTicketManagement"
          component={SupportTicketManagementScreen}
        />
        <MainStack.Screen name="Feedback" component={FeedbackPluginScreen} />
        <MainStack.Screen
          name="VideoCallScreen"
          component={CallCenterScreen}
          options={{ gestureEnabled: false }}
        />
        <MainStack.Screen name="Authorize" component={AuthorizeScreen} />
        <MainStack.Screen
          name="ITOPManagement"
          component={ITOPManagementScreen}
        />
        <MainStack.Screen
          name="CustomerService"
          component={CustomerServiceScreen}
        />
        <MainStack.Screen name="PFController" component={PFControllerScreen} />
        <MainStack.Screen name="TimeKeeping" component={TimeKeepingPlugin} />
        <MainStack.Screen
          name="ChangeHealthLocation"
          component={ChangeHealthLocationPlugin}
        />
        <MainStack.Screen
          name="WorkplaceAssessment"
          component={WorkplaceAssessmentPlugin}
        />
        <MainStack.Screen
          name="CardLinkMembership"
          component={CardLinkMembershipPlugin}
        />
        <MainStack.Screen
          name="ApprovePhoneNumber"
          component={ApprovePhoneNumberPlugin}
        />
        <MainStack.Screen
          name="DiscountedPurchaseApproval"
          component={DiscountedPurchaseApprovalPlugin}
        />
        <MainStack.Screen
          name="EvaluationSurvey"
          component={EvaluationSurveyPlugin}
        />
        <MainStack.Screen
          name="OsinEvaluation"
          component={OsinEvaluationPlugin}
        />
        <MainStack.Screen name="PaymentApproval" component={PaymentApproval} />
        <MainStack.Screen name="Mail" component={MailPlugin} />
        <MainStack.Screen name="TrackingOrder" component={TrackOrderPlugin} />
        <MainStack.Screen
          name="GenerateBarcode"
          component={GenerateBarcodePlugin}
        />
        <MainStack.Screen
          name="OutputVoucherLogRequest"
          component={OutputVoucherLogRequestPlugin}
        />
        <MainStack.Screen
          name="DeviceProfile"
          component={DeviceProfilePlugin}
        />
        <MainStack.Screen
          name="QCProfileManagement"
          component={QCProfileManagementPlugin}
        />
        <MainStack.Screen
          name="DeclarationElectricAndWater"
          component={DeclarationElectricAndWaterPlugin}
        />
        <MainStack.Screen
          name="BotSupportTicket"
          component={BotSupportTicketPlugin}
        />
        <MainStack.Screen
          name="Deliveries"
          component={DeliveriesPlugin}
        />
        <MainStack.Screen name="AssetMWG" component={AssetMWGPlugin} />
      </MainStack.Navigator>
      <Feedback
        isShowFeedback={() => {
          navigation.navigate('Feedback');
        }}
        branchMain={'XWork'}
      />
    </>
  );
};

export default MainAppNavigator;

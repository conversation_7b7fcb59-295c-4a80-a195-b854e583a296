import { D<PERSON><PERSON>ogo, WrapperContainerHome } from '@components';
import { Mixins } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import React, { Component } from 'react';
import { pickMultiple, pickSingleWithCamera, storage } from '@common';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  Linking,
  ScrollView,
  RefreshControl,
  AppState,
  Platform,
  Image,
  ImageBackground,
} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionDashboardCreator from '../Dashboard/action';
import * as _actionHome from './action';
import * as actionNotifi from '../Notification/action';
import * as _actionLogin from '../Login/action';
import { XworkColor } from '@mwg-sdk/styles';
import { helper } from '@common';
import { ENUM_STORAGE, _UploadFile, constants } from '@constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import { ModalSelectedGroup } from '../../components';
import { NotifService } from '../../common';
import handlerNotifyType from './components/handlerNotifyType';
import ListTicket from './ComponentHome/ListTicket';
import ListUtilities from './ComponentHome/ListUtilities ';
import ListGroupTicket from './ComponentHome/ListGroupTicket';
import { translate } from '@mwg-kits/languages';
import { requestPermission } from '@mwg-kits/core';
import { changeLanguage, reloadAll } from '@mwg-kits/languages';
import { DynamicTheme } from '@components';

class HomeScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showModalBottom: false,
      isShowModalQuickTicket: false,
      listImage: [],
      refreshingHome: false,
      isShowModalSelectedGroup: false,
      itemGroupSelected: null,
      currentAppState: AppState.currentState === 'active',
      lostConnection: false,
      refreshAPI: false,
      isLengthUser: 10,
    };
    this.client = null;
    this.notif = new NotifService();
    this.checkPermissionOnAppOpen();
  }

  defaultTextPriority = id => {
    switch (id) {
      case 1:
        return XworkColor.GRAYF7;
      case 2:
        return XworkColor.DARK_BLUE_40;
      case 3:
        return XworkColor.DARK_YELLOW_30;
      case 4:
        return XworkColor.DARK_RED_40;

      default:
        return XworkColor.GRAYF7;
    }
  };

  onPressModalQuickTicket = () => {
    this.setState({
      isShowModalQuickTicket: true,
    });
  };
  onChangePicker = selectedImages => {
    const arr = selectedImages.map(img => {
      return {
        ...img,
      };
    });
    this.setState({ listImage: [...arr, ...this.state.listImage] });
  };
  onRemoveImages = item => {
    const { listImage } = this.state;
    const itemWillDelete = listImage.find(deleteItem => {
      return deleteItem === item;
    });
    const newsArr = listImage.filter(_item => {
      return _item !== itemWillDelete;
    });

    this.setState({
      listImage: newsArr,
    });
  };

  handleNavigation = screenName => {
    if (screenName === 'Life at MWG') {
      Linking.openURL('https://www.facebook.com/LifeAtMWG');
    } else if (screenName === 'Xticket') {
      this.props.navigation.navigate(screenName);
      this.props.actionDashboard.changeScreenName(screenName);
    } else {
      this.props.navigation.navigate(screenName);
    }
  };

  init = () => {
    this.props.actionHome.getListGroupTicket();
    this.props.actionHome.getListTicket();
    this.props.actionHome.getApproveTypeList();
    this.props.actionHome.getUtilitiesUsed();
    this.props.actionHome.getUserPer();
  };

  async componentDidMount() {

    // await this.checkInternet();
    this.addLinkingListener();
    this.props.navigation.addListener('focus', async () => {
      this.checkInternet();
    });
    this.init();

    this.handlerNotfy = new handlerNotifyType({
      navigation: this.props.navigation,
      actionDashboard: this.props.actionDashboard,
      actionNotifi: this.props.actionNotifi,
    });
    // this.handlerNotfy.connectWS();
    // this.handlerNotfy.listenerFromNotify();
    this.appStateSubscription = AppState.addEventListener(
      'change',
      nextAppState => {
        this.setState({ currentAppState: nextAppState }, () => {
          if (this.state.currentAppState === 'active') {
            // this.handlerNotfy.listenerFromNotify();
            // this.handlerNotfy.connectWS();
            this.props.actionNotifi.getCountNotify();
          }
          if (this.state.currentAppState !== 'active') {
            this.handlerNotfy.logOutWS();
          }
        });
      },
    );

    this.checkPermissionOnAppOpen();
    this.props.actionLogin.checkVersionApp();

    this.props.actionHome.getListUtilities({
      search: '',
      lang: this.props.currentLang ?? 'vi',
      pageReq: {
        pageNumber: 1,
        pageSize: 20,
      },
      status: 'ACTIVE',
    });

    this.props.actionHome
      .getUserBell()
      .then(res => {
        if (Platform.OS === 'android') {
          storage.setItem('NotificationChannelId', res.channelId);
          this.handlerNotfy.notif.createOrUpdateChannel(
            res.channelId,
            res.bellName,
          );
        } else {
          storage.setItem('NotificationSound', res.bellName);
        }
      })
      .catch(err => {
        console.log('Xảy ra lỗi khi lấy Bell', err);
      });
  }
  refreshError = async () => {
    try {
      this.setState({
        refreshAPI: true,
      });
      await this.checkInternet();
      await this.init();
      this.setState({
        refreshAPI: false,
      });
    } catch (err) {
      console.error(err, 'ERRROR APIII');
    }
  };

  componentDidUpdate = prevProps => {
    const { needToUpdate, currentLang } = this.props;
    if (needToUpdate !== prevProps.needToUpdate && constants.ISLIVE) {
      if (needToUpdate) {
        this.upgradeApp();
      }
    }
    if (currentLang !== prevProps.currentLang) {
      this.props.actionHome.getListUtilities({
        search: '',
        lang: this.props.currentLang ?? 'vi',
        pageReq: {
          pageNumber: 1,
          pageSize: 20,
        },
        status: 'ACTIVE',
      });
    }
  };

  upgradeApp = () => {
    const { versionServer, currentLang } = this.props;
    global.props.alert({
      show: true,
      message:
        currentLang === 'vi'
          ? `Ứng dụng hiện đã có phiên bản cập nhật (${versionServer}) mới!`
          : `The application now has a new updated version (${versionServer}).`,
      type: 'info',
      confirmText: translate('update'),

      onConfirmPressed: () => {
        Linking.openURL(
          'xmanager://xmanager?action=update&bundleID=mwg.tgdd.xwork',
        )
          .then(() => console.log('success'))
          .catch(error => {
            const url_download =
              Platform.OS == 'ios'
                ? 'https://app.tgdd.vn/'
                : 'https://play.google.com/store/apps/details?id=mwg.tgdd.xwork';
            Linking.openURL(url_download);
            console.log('error upgrade==', error);
          });
        global.props.alert({ show: false });
      },
    });
  };

  checkPermissionOnAppOpen = async () => {
    const hasPermissionChecked = storage.getItem(
      'hasPermissionChecked',
    );

    if (!hasPermissionChecked) {
      this.requestPermission2();
      storage.setItem('hasPermissionChecked', 'true');
    }
  };

  requestPermission2 = async () => {
    if (Platform.OS === 'android') {
      try {
        await requestPermission('photo');
        await requestPermission('storage');
      } catch (error) {
        console.log('error', error);
      }
    }
  };

  getUrlAsyncLinking = async () => {
    const initialUrl = await Linking.getInitialURL();
    this.checkUrlXticket(initialUrl);
  };
  checkUrlXticket = async url => {
    if (
      helper.checkDeepLinkTicket(url) !== '' &&
      helper.IsValidateObject(helper.checkDeepLinkTicket(url))
    ) {
      storage.setItem(
        ENUM_STORAGE.TICKET_ID,
        JSON.stringify({
          id: helper.checkDeepLinkTicket(url),
          indexHeader: 0,
        }),
      );
      this.props.actionDashboard.changeScreenName('DetailTicket');
      this.props.navigation.navigate('Xticket');
      return;
    }
  };
  addLinkingListener = () => {
    this.getUrlAsyncLinking();
    Linking.addEventListener('url', ({ url }) => {
      this.checkUrlXticket(url);
    });
  };

  onPressDetailGroup = async () => {
    this.setState({
      isShowModalSelectedGroup: false,
    });
    storage.setItem(
      ENUM_STORAGE.PARAMS_XTICKET,
      JSON.stringify(this.state.itemGroupSelected),
    );
    this.props.navigation.navigate('Xticket');
    this.props.actionDashboard.changeScreenName('DetailGroup');
  };
  handleOutGroup = async () => {
    this.setState({
      isShowModalSelectedGroup: false,
    });
    global.props.alert({
      show: true,
      title: translate('leave_group'),
      titleColor: { color: XworkColor.DARK_RED_30 },
      message: translate('confirm_leave_group'),
      confirmText: translate('leave_group'),
      confirmButtonTextStyle: {
        color: XworkColor.DARK_RED_30,
      },
      cancelText: translate('cancel'),
      cancelButtonTextStyle: {
        color: XworkColor.GRAYF7,
      },
      onConfirmPressed: () => {
        global.props.alert({ show: false });
        this.confirmOutGroup();
      },
      onCancelPressed: () => {
        global.props.alert({ show: false });
      },
    });
  };
  ////
  confirmOutGroup = async () => {
    const { fullProfile } = this.props;

    const body = {
      supportServiceId: this.state.itemGroupSelected.id,
      userId: fullProfile.id,
    };

    const responeRemoveMember = await this.props.actionHome.removeMemberGroup(
      body,
    );
    this.props.actionHome.getListGroupTicket();
    if (responeRemoveMember && !responeRemoveMember.error) {
      Toast.show({
        type: 'success',
        text1: translate('success_leave_group'),
        position: 'bottom',
      });
      setTimeout(() => {
        Toast.hide();
      }, 1000);
    } else {
      Toast.show({
        type: 'error',
        text1: translate('fail_leave_group'),
        position: 'bottom',
      });
    }
  };
  handleRemove = async () => {
    try {
      global.props.showLoader();

      const data = {
        supportServiceId: this.state.itemGroupSelected?.id,
        action: 'DELETE',
      };
      const successRemove = await this.props.actionHome.deleteGroup(data);
      global.props.hideLoader();
      if (successRemove) {
        this.props.actionHome.getListGroupTicket();
        Toast.show({
          type: 'success',
          text1: translate('success_delete_group'),
          position: 'bottom',
        });
        setTimeout(() => {
          Toast.hide();
        }, 1000);
      }
    } catch (error) {
      global.props.hideLoader();
      Toast.show({
        type: 'error',
        text1: translate('fail_delete_group'),
        position: 'bottom',
      });
    }
  };
  removeGroup = () => {
    this.setState({
      isShowModalSelectedGroup: false,
    });
    global.props.alert({
      show: true,
      title: translate('delete_group'),
      titleColor: { color: XworkColor.DARK_RED_30 },
      message: translate('confirm_delete_group'),
      confirmText: translate('delete'),
      confirmButtonTextStyle: {
        color: XworkColor.DARK_RED_30,
      },
      cancelText: translate('cancel'),
      cancelButtonTextStyle: {
        color: XworkColor.GRAYF7,
      },

      onConfirmPressed: () => {
        global.props.alert({ show: false });
        this.handleRemove();
      },
      onCancelPressed: () => {
        global.props.alert({ show: false });
      },
    });
  };
  onPressBottomAction = item => {
    if (item.id === 0) {
      this.setState(
        {
          showModalBottom: false,
        },
        () => {
          this.onPressModalQuickTicket();
        },
      );
    }
    if (item.id === 1) {
      this.props.navigation.navigate('Xticket');
      this.props.actionDashboard.changeScreenName('CreateTicket');
    }
    if (item.id === 2) {
      this.onPressButtonPlus();
    }
  };
  onPressShowModalGroup = item => {
    this.setState({
      isShowModalSelectedGroup: true,
      itemGroupSelected: item,
    });
  };
  onRefresh = async () => {
    await this.checkInternet();
    this.setState(
      {
        refreshingHome: true,
      },
      () => {
        setTimeout(() => {
          this.init();
          this.setState({
            refreshingHome: false,
          });
        }, 500);
      },
    );
  };
  checkInternet = async () => {
    try {
      NetInfo.refresh().then(async state => {
        if (state.isConnected) {
          // await helper.reFetchingLang();
          setTimeout(() => {
            this.setState({
              lostConnection: false,
            });
          }, 500);
          reloadAll();
        } else {
          // changeLanguage('defaultEn')
          const langAsyncStore = storage.getItem('LANGUAGE');
          const parseLang = JSON.parse(langAsyncStore);
          changeLanguage(parseLang === 'en' ? 'defaultEn' : 'defaultVi');
          setTimeout(() => {
            this.setState({
              lostConnection: true,
            });
          }, 500);
          reloadAll();
        }
      });
    } catch (err) {
      console.log('====================================');
      console.log(err, '11111111');
      console.log('====================================');
    }
  };
  onPressButtonPlus = () => {
    this.setState({
      showModalBottom: !this.state.showModalBottom,
    });
  };

  render() {
    const { currentLang, listGroupTicket } = this.props;
    const COLOR_LOADING = '#9FC6FF';


    return (
      <DynamicTheme>
        <View
          style={{
            flex: 1,
            zIndex: 10,
            marginTop: global.props.insets.top,
          }}>
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              height: Mixins.scale(56),
            }}>
            <DynamicLogo defaultSize={22} holidaySize={106} textSize={6} />
          </View>
          <ScrollView
            nestedScrollEnabled
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.styleContent}
            refreshControl={
              <RefreshControl
                progressViewOffset={0}
                refreshing={this.state.refreshingHome}
                onRefresh={this.onRefresh}
                tintColor={COLOR_LOADING}
                XworkColor={[COLOR_LOADING]}
              />
            }>
            <View
              style={{
                marginBottom: Mixins.scale(12),
                paddingHorizontal: Mixins.scale(16),
              }}>
              <ListUtilities
                props={this.props}
                navigation={this.props.navigation}
                currentLang={currentLang}
              />
            </View>
            <View style={styles.container}>
              <WrapperContainerHome
                hideHeader
                customTextError={translate('error_api')}
                isError={
                  this.props.listTicket.isError ||
                  this.props.listGroupTicket.isError
                }
                isLoading={this.state.refreshAPI}
                actionRetry={this.refreshError}
                navigation={this.props.navigation}>
                {/* {helper.IsEmptyArray(listGroupTicket?.data) ? null : (
                  <ListGroupTicket
                    props={this.props}
                    navigation={this.props.navigation}
                    onPressModalQuickTicket={this.onPressModalQuickTicket}
                    onPressShowModalGroup={this.onPressShowModalGroup}
                    currentLang={currentLang}
                  />
                )} */}
                <ListTicket
                  props={this.props}
                  navigation={this.props.navigation}
                  defaultTextPriority={this.defaultTextPriority}
                  currentLang={currentLang}
                />
              </WrapperContainerHome>
            </View>
          </ScrollView>
          {this.state.isShowModalSelectedGroup && (
            <ModalSelectedGroup
              isVisible={this.state.isShowModalSelectedGroup}
              itemGroupSelected={this.state.itemGroupSelected}
              onPressDetailGroup={this.onPressDetailGroup}
              onPressLeaveGroup={this.handleOutGroup}
              onPressRemoveGroup={this.removeGroup}
              onPressDimiss={() => {
                this.setState({
                  isShowModalSelectedGroup: false,
                });
              }}
            />
          )}
          <View style={styles.btnPlus}>
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.navigate('CreateQuickTicket');
              }}
              style={styles.btnBottom}>
              <AntDesign name="plus" size={24} color={XworkColor.WHITE} />
            </TouchableOpacity>
          </View>
        </View>
      </DynamicTheme>
    );
  }
}

const mapStateToProps = function (state) {
  return {
    listTicket: state.homeReducer.listTicket,
    listMyNote: state.homeReducer.listMyNote,
    listGroupTicket: state.homeReducer.listGroupTicket,
    listHashtag: state.homeReducer.listHashtag,
    profile: state.profileReducer.data,
    fullProfile: state.profileReducer.fullProfile.data,
    utilitiesUsed: state.homeReducer.utilitiesUsed,
    currentLang: state.homeReducer.currentLang,
    versionServer: state.loginReducer.versionServer,
    needToUpdate: state.loginReducer.needToUpdate,
    listUser: state.homeReducer.listUser,
    incomingCallData: state.videoCallReducer.incomingCallData,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    actionHome: bindActionCreators(_actionHome, dispatch),
    actionDashboard: bindActionCreators(actionDashboardCreator, dispatch),
    actionNotifi: bindActionCreators(actionNotifi, dispatch),
    actionLogin: bindActionCreators(_actionLogin, dispatch),
  };
};
export default connect(mapStateToProps, mapDispatchToProps)(HomeScreen);
const styles = StyleSheet.create({
  btnBottom: {
    alignItems: 'center',
    backgroundColor: XworkColor.DARK_BLUE_60,
    borderRadius: Mixins.scale(28),
    height: Mixins.scale(56),
    justifyContent: 'center',
    width: Mixins.scale(56),
  },
  containerApp: {
    flex: 1,
    width: '100%',
    zIndex: 1000,
    backgroundColor: XworkColor.WHITE,
  },
  btnPlus: {
    bottom: 10,
    position: 'absolute',
    right: Mixins.scale(10),
  },
  container: {
    // backgroundColor: XworkColor.WHITE,
    paddingHorizontal: Mixins.scale(12),
  },

  img_background: {
    backgroundColor: 'red',
    height: Mixins.scale(350),
    paddingTop: Mixins.scale(43),
    width: '100%',
    zIndex: 1,
  },
  img_logoXwork: {
    alignItems: 'center',
    height: Mixins.scale(42),
    justifyContent: 'center',
    width: '100%',
  },
  styleContent: {
    flexGrow: 1,
    paddingBottom: Mixins.scale(80),
  },
  viewLogo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: Mixins.scale(42),
  },
  txtXwork: {
    textAlign: 'center',
    maxWidth: Mixins.scale(300),
    color: XworkColor.BLUE_MAIN,
  },
  imgXwork: {
    height: Mixins.scale(22),
    width: Mixins.scale(22),
  },
});
